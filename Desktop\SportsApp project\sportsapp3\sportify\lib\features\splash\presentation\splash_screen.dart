import 'package:flutter/material.dart';
import 'package:sportify/features/onboarding/presentation/onboarding_screen.dart';
import 'package:sportify/features/auth/presentation/login_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _navigateToNextScreen();
  }

  Future<void> _navigateToNextScreen() async {
    try {
      // Wait for 2 seconds
      await Future.delayed(const Duration(seconds: 2));

      // Get shared preferences instance
      final prefs = await SharedPreferences.getInstance();

      // Check if user has completed onboarding
      final bool onboardingCompleted =
          prefs.getBool('onboardingCompleted') ?? false;

      // Check if user is logged in
      final bool isLoggedIn = prefs.getBool('isLoggedIn') ?? false;

      if (!mounted) return;

      // Navigate to appropriate screen
      if (isLoggedIn) {
        Navigator.pushReplacementNamed(context, '/home');
      } else if (onboardingCompleted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      } else {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const OnboardingScreen()),
        );
      }
    } catch (e) {
      // Fallback navigation if shared preferences fails
      if (!mounted) return;
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const OnboardingScreen()),
      );
      print('Error accessing shared preferences: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          width: double.infinity,
          height: double.infinity,
          color: const Color.fromARGB(255, 43, 125, 239),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              SizedBox(height: 100),
              Container(
                child: Column(
                  children: [
                    SizedBox(height: 40),
                    Icon(
                      Icons.check_circle_rounded,
                      size: 100,
                      color: Colors.white,
                    ),
                    Text(
                      'Sportify',
                      style: TextStyle(
                        fontSize: 60,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 10),
                    Text(
                      'Your Daily Dose of Sports News',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.normal,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              SizedBox(height: 20),
              CircularProgressIndicator(color: Colors.white),
              Text(
                'Loading...',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
